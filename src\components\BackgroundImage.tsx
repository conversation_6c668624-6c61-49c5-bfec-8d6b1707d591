
import { useEffect, useRef } from 'react';
import { useIsMobile } from '../hooks/use-mobile';

interface BackgroundImageProps {
  src: string;
  mobileSrc?: string;
  alt?: string;
}

const BackgroundImage = ({ src, mobileSrc, alt = "Background" }: BackgroundImageProps) => {
  const bgRef = useRef<HTMLDivElement>(null);
  const isMobile = useIsMobile();
  const imageSrc = isMobile && mobileSrc ? mobileSrc : src;
  
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!bgRef.current || isMobile) return;
      
      const x = e.clientX / window.innerWidth;
      const y = e.clientY / window.innerHeight;
      
      // Very subtle parallax effect for portrait
      const moveX = (x - 0.5) * 5; // Reduced movement for portrait
      const moveY = (y - 0.5) * 5;
      
      bgRef.current.style.transform = `translate(${moveX}px, ${moveY}px) scale(1.05)`;
    };
    
    window.addEventListener('mousemove', handleMouseMove);
    
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, [isMobile]);
  
  return (
    <div className="fixed inset-0 z-0 overflow-hidden">
      <div 
        ref={bgRef}
        className={`parallax-bg w-[105%] h-[105%] ${isMobile ? '' : 'transition-transform duration-700 ease-out'}`}
        style={{ 
          backgroundImage: `url(${imageSrc})`,
          backgroundPosition: 'center center',
          backgroundSize: 'cover',
          objectFit: 'cover',
          objectPosition: 'center',
          transform: isMobile ? 'none' : 'scale(1.05)'
        }}
        aria-label={alt}
      />
      <div className="absolute inset-0 bg-gradient-to-b from-black/30 to-black/50" />
    </div>
  );
};

export default BackgroundImage;


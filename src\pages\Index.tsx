
import { useState, useEffect } from 'react';
import BackgroundImage from '../components/BackgroundImage';
import Navigation from '../components/Navigation';
import InfoDots from '../components/InfoDots';
import { useIsMobile } from '../hooks/use-mobile';
import { X } from 'lucide-react';

// Updating the desktop background image URL
const backgroundImage = "/lovable-uploads/me4.png";
const mobileBackgroundImage = "/lovable-uploads/08aeec50-4c42-4d84-b401-18b296809771.png";

const Index = () => {
  const [activePopup, setActivePopup] = useState<string | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [showInfoTip, setShowInfoTip] = useState(false);
  const isMobile = useIsMobile();

  useEffect(() => {
    // Simulate loading and create smooth entrance animation
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 300);
    
    // Show the info tip after a delay
    const infoTipTimer = setTimeout(() => {
      setShowInfoTip(true);
    }, 1500);
    
    return () => {
      clearTimeout(timer);
      clearTimeout(infoTipTimer);
    };
  }, []);

  const openPopup = (id: string) => {
    setActivePopup(id);
  };

  const closePopup = () => {
    setActivePopup(null);
  };

  return (
    <div className="relative h-full w-full overflow-hidden">
      <BackgroundImage 
        src={backgroundImage} 
        mobileSrc={mobileBackgroundImage} 
        alt="Portrait photo" 
      />
      
      {/* Info Dots Tip Popup - Only show on desktop */}
      {showInfoTip && !isMobile && (
        <div className="fixed top-4 left-4 z-50">
          <div className="glass-dark p-4 rounded-lg flex items-center gap-4">
            <button 
              onClick={() => setShowInfoTip(false)}
              className="text-white/80 hover:text-white transition-colors mr-2"
            >
              <X size={16} />
            </button>
            <span className="text-white text-sm">Tip: hover over the dots</span>
          </div>
        </div>
      )}
      
      <div className={`absolute z-10 transition-opacity duration-1000 ${
        isMobile 
          ? 'inset-0 flex flex-col justify-center items-center px-6' 
          : 'bottom-16 left-16 flex flex-col items-start'
        } ${isLoaded ? 'opacity-100' : 'opacity-0'}`}>
        <h1 className={`main-title leading-none animate-fade-in ${
          isMobile 
            ? 'text-6xl sm:text-7xl text-center' 
            : 'text-7xl sm:text-8xl md:text-9xl text-left font-medium tracking-normal'
          }`}>
          LÉON<br />MISSOUL
        </h1>
      </div>
      
      <Navigation onItemClick={openPopup} activeItem={activePopup} />
      <InfoDots />
      
      {/* Email Popup */}
      {activePopup === 'email' && (
        <>
          <div 
            className="fixed inset-0 bg-black/70 backdrop-blur-sm z-40"
            onClick={closePopup}
          />
          <div className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50 p-8 rounded-xl glass-dark">
            <div className="flex items-center justify-between w-full">
              <a 
                href="mailto:<EMAIL>" 
                className="text-white text-xl md:text-2xl hover:text-white/80 transition-colors mr-4"
              >
                <EMAIL>
              </a>
              <button 
                className="p-1 rounded-full hover:bg-white/10 transition-colors text-white/80 hover:text-white"
                onClick={closePopup}
              >
                <X size={18} />
              </button>
            </div>
          </div>
        </>
      )}
      
      {/* Phone Popup */}
      {activePopup === 'phone' && (
        <>
          <div 
            className="fixed inset-0 bg-black/70 backdrop-blur-sm z-40"
            onClick={closePopup}
          />
          <div className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50 p-8 rounded-xl glass-dark">
            <div className="flex items-center justify-between w-full">
              <a 
                href="tel:+32472972106" 
                className="text-white text-xl md:text-2xl hover:text-white/80 transition-colors mr-4"
              >
                +32 472 97 21 06
              </a>
              <button 
                className="p-1 rounded-full hover:bg-white/10 transition-colors text-white/80 hover:text-white"
                onClick={closePopup}
              >
                <X size={18} />
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default Index;

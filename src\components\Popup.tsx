
import { useEffect, useRef } from 'react';
import { X } from 'lucide-react';

interface PopupProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

const Popup = ({ isOpen, onClose, title, children }: PopupProps) => {
  const contentRef = useRef<HTMLDivElement>(null);
  const overlayRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };
    
    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }
    
    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = '';
    };
  }, [isOpen, onClose]);
  
  useEffect(() => {
    if (!contentRef.current || !overlayRef.current) return;
    
    if (isOpen) {
      contentRef.current.style.visibility = 'visible';
      overlayRef.current.style.visibility = 'visible';
      
      // Add small delay for enter animation
      setTimeout(() => {
        if (contentRef.current && overlayRef.current) {
          contentRef.current.style.opacity = '1';
          contentRef.current.style.transform = 'translate(-50%, -50%) scale(1)';
          overlayRef.current.style.opacity = '1';
        }
      }, 50);
    } else {
      if (contentRef.current && overlayRef.current) {
        contentRef.current.style.opacity = '0';
        contentRef.current.style.transform = 'translate(-50%, -50%) scale(0.95)';
        overlayRef.current.style.opacity = '0';
        
        // Wait for exit animation before hiding
        setTimeout(() => {
          if (contentRef.current && overlayRef.current) {
            contentRef.current.style.visibility = 'hidden';
            overlayRef.current.style.visibility = 'hidden';
          }
        }, 300);
      }
    }
  }, [isOpen]);
  
  if (!isOpen) return null;
  
  return (
    <>
      <div 
        ref={overlayRef}
        className="fixed inset-0 bg-black/70 backdrop-blur-sm z-40 opacity-0"
        style={{ visibility: 'hidden', transition: 'opacity 300ms ease-out' }}
        onClick={onClose}
      />
      <div 
        ref={contentRef}
        className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50 w-[90vw] max-w-3xl max-h-[80vh] overflow-y-auto rounded-2xl glass p-0 opacity-0"
        style={{ 
          visibility: 'hidden', 
          transition: 'opacity 300ms ease-out, transform 300ms ease-out',
          transform: 'translate(-50%, -50%) scale(0.95)'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="relative">
          {/* Header with frosted glass effect */}
          <div className="sticky top-0 backdrop-blur-md bg-white/10 border-b border-white/10 px-8 py-6 flex justify-between items-center">
            <h2 className="text-2xl font-light tracking-tight text-white">{title}</h2>
            <button 
              onClick={onClose}
              className="rounded-full p-2 hover:bg-white/10 transition-colors text-white/80 hover:text-white"
              aria-label="Close"
            >
              <X size={20} />
            </button>
          </div>
          
          {/* Content area with proper padding */}
          <div className="p-8">
            <div className="prose prose-sm max-w-none prose-invert">
              {children}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Popup;

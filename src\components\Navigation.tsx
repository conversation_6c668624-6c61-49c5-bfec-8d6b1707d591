
import { useState } from 'react';
import { Mail, Phone, Instagram, Github, Linkedin, Info } from 'lucide-react';
import { useIsMobile } from '../hooks/use-mobile';

interface NavigationProps {
  onItemClick: (item: string) => void;
  activeItem: string | null;
}

const Navigation = ({ onItemClick, activeItem }: NavigationProps) => {
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const isMobile = useIsMobile();
  
  const navItems = [
    { id: 'info', icon: <Info size={isMobile ? 16 : 18} />, label: 'Info' },
    { id: 'email', icon: <Mail size={isMobile ? 16 : 18} />, label: 'Email' },
    { id: 'phone', icon: <Phone size={isMobile ? 16 : 18} />, label: 'Phone' },
  ];
  
  const socialItems = [
    { id: 'linkedin', icon: <Linkedin size={isMobile ? 14 : 16} />, label: 'LinkedIn', url: 'https://www.linkedin.com/in/l%C3%A9on-missoul-293b95179/' },
    { id: 'instagram', icon: <Instagram size={isMobile ? 14 : 16} />, label: 'Instagram', url: 'https://www.instagram.com/missoulleon/' },
    { id: 'github', icon: <Github size={isMobile ? 14 : 16} />, label: 'GitHub', url: 'https://github.com/missoulleon' },

  ];
  
  const handleNavItemClick = (item: any) => {
    onItemClick(item.id);
  };
  
  return (
    <div className={`fixed z-30 flex flex-col items-center ${
      isMobile ? 'bottom-8 right-0 left-0 mx-auto w-auto' : 'right-8 top-1/2 -translate-y-1/2'
    }`}>
      <div className={`${isMobile ? 'flex justify-center space-x-4 mb-6' : 'mb-12'}`}>
        {navItems.map((item) => (
          <div 
            key={item.id}
            className={`nav-item group ${activeItem === item.id ? 'active' : ''} ${
              isMobile ? 'w-9 h-9' : 'w-10 h-10 mb-4'
            }`}
            onClick={() => handleNavItemClick(item)}
            onMouseEnter={() => setHoveredItem(item.id)}
            onMouseLeave={() => setHoveredItem(null)}
          >
            {item.icon}
            
            {!isMobile && (
              <div className={`absolute right-full mr-4 px-3 py-1 rounded glass-dark text-white text-sm whitespace-nowrap
                           transition-all duration-300 ${hoveredItem === item.id ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-2'}`}>
                {item.label}
              </div>
            )}
          </div>
        ))}
      </div>
      
      <div className={`flex ${isMobile ? 'flex-row space-x-3' : 'flex-col items-center'}`}>
        {socialItems.map((item) => (
          <a 
            key={item.id}
            href={item.url}
            target="_blank"
            rel="noopener noreferrer"
            className={`nav-item ${isMobile ? 'w-7 h-7' : 'w-8 h-8 mb-3'} text-white/80 hover:text-white`}
            onMouseEnter={() => setHoveredItem(item.id)}
            onMouseLeave={() => setHoveredItem(null)}
          >
            {item.icon}
            
            {!isMobile && (
              <div className={`absolute right-full mr-4 px-3 py-1 rounded glass-dark text-white text-sm whitespace-nowrap
                           transition-all duration-300 ${hoveredItem === item.id ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-2'}`}>
                {item.label}
              </div>
            )}
          </a>
        ))}
      </div>
    </div>
  );
};

export default Navigation;

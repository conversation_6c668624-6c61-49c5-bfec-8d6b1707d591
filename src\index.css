
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  * {
    @apply border-border selection:bg-black/10 selection:text-black;
  }

  html, body {
    @apply overflow-hidden h-full w-full;
    font-family: 'Inter', sans-serif;
  }

  body {
    @apply bg-background text-foreground h-full w-full;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02", "cv03";
  }

  #root {
    @apply h-full w-full m-0 p-0;
  }
}

@layer components {
  .glass {
    @apply backdrop-blur-md bg-white/20 border border-white/30 shadow-lg;
  }
  
  .glass-dark {
    @apply backdrop-blur-md bg-black/20 border border-white/10 shadow-lg;
  }
  
  .nav-item {
    @apply relative flex items-center justify-center w-10 h-10 mb-4 rounded-full 
           glass-dark text-white/90 transition-all duration-300
           hover:bg-white/30 hover:text-white;
  }
  
  .nav-item.active {
    @apply bg-white/40 text-white;
  }
  
  .parallax-bg {
    @apply fixed inset-0 bg-cover bg-center transition-transform duration-700 ease-out;
    transform: scale(1.05);
  }
  
  .popup-content {
    @apply fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50
           w-[90vw] max-w-3xl max-h-[80vh] overflow-y-auto 
           rounded-2xl glass p-8 opacity-0;
    visibility: hidden;
    transition: opacity 300ms ease-out, transform 300ms ease-out;
  }
  
  .popup-overlay {
    @apply fixed inset-0 bg-black/70 backdrop-blur-sm z-40 opacity-0;
    visibility: hidden;
    transition: opacity 300ms ease-out;
  }
  
  .main-title {
    @apply font-medium tracking-tight text-white text-right;
    text-shadow: 0 4px 30px rgba(0, 0, 0, 0.4);
  }
  
  .subtle-shadow {
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
}

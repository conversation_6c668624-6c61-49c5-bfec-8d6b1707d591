
import { useState, useEffect } from 'react';
import { useIsMobile } from '../hooks/use-mobile';

interface InfoDot {
  id: string;
  position: {
    top: string;
    right: string;
  };
  fact: string;
}

const InfoDots = () => {
  const [hoveredDot, setHoveredDot] = useState<string | null>(null);
  const [infoDots, setInfoDots] = useState<InfoDot[]>([]);
  const isMobile = useIsMobile();
  
  useEffect(() => {
    // Generate random positions for dots
    const facts = [
      "I'm studying Industrial Engineering at the University of Ghent with a specialisation in software. Expected to graduate in 2027.",
      "I founded an artist management agency called Crash Events back in 2023, in which we did close to 60K our first year in business and is still growing as we speak.",
      "I built an IT service business which started out as freelance work. As of april 2025, our team consists of 3 people and growing.",
      "I'm an explorer. I love the unknown. I love learning and meeting new people. I love the thrill of the chase.",
      "I'm born in 2005. I'm young, driven and ambitious. I'm a Gen Z entrepreneur.",
      "Currently in the process of building StageCloud. A platform to find and book DJ's for ANY event. The booking.com for DJ's.",
    ];
    
    const randomizedDots = facts.map((fact, index) => {
      // Random top position between 10% and 90%
      const topPosition = `${10 + Math.floor(Math.random() * 80)}%`;
      
      // Random right position between 50% and 75% of the right side
      const rightPosition = `${50 + Math.floor(Math.random() * 25)}%`;
      
      return {
        id: `fact${index + 1}`,
        position: {
          top: topPosition,
          right: rightPosition
        },
        fact
      };
    });
    
    setInfoDots(randomizedDots);
  }, []);
  
  // Skip rendering on mobile
  if (isMobile) return null;
  
  return (
    <div className="fixed inset-y-0 right-0 w-1/2 z-20 pointer-events-none">
      {infoDots.map((dot) => (
        <div 
          key={dot.id}
          className="absolute pointer-events-auto"
          style={{ top: dot.position.top, right: dot.position.right }}
        >
          <div 
            className="relative"
            onMouseEnter={() => setHoveredDot(dot.id)}
            onMouseLeave={() => setHoveredDot(null)}
          >
            <div className="w-3 h-3 rounded-full bg-white/70 backdrop-blur-sm hover:bg-white transition-all cursor-pointer"></div>
            
            <div className={`absolute top-0 right-full mr-4 w-64 p-4 rounded glass-dark text-white text-sm
                         transition-all duration-300 ${
                           hoveredDot === dot.id 
                             ? 'opacity-100 translate-x-0' 
                             : 'opacity-0 translate-x-2 pointer-events-none'
                         }`}>
              {dot.fact}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default InfoDots;


<!DOCTYPE html>
<html lang="nl" dir="ltr" itemscope itemtype="http://schema.org/WebPage">
  <head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-PH5WW3NV75"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-PH5WW3NV75');
    </script>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0" />
    <meta name="theme-color" content="#000000" />
    <meta name="geo.region" content="BE" />
    <meta name="geo.placename" content="Gent" />
    <meta name="content-language" content="nl-BE" />

    <!-- Primary Meta Tags -->
    <title>Léon Missoul | Software Ontwikkelaar & Ondernemer uit Brugge</title>
    <meta name="title" content="Léon Missoul | Software Ontwikkelaar & Ondernemer uit Brugge">
    <meta name="description" content="Student Industrieel Ingenieur aan de Universiteit Gent met specialisatie in software. Oprichter van Crash Events artiestenbureau en een IT-servicebedrijf. Jonge ondernemer met passie voor technologie en innovatie.">
    <meta name="keywords" content="Léon Missoul, software ontwikkelaar, programmeur, ondernemer, industrieel ingenieur, Universiteit Gent, UGent, Crash Events, IT diensten, webontwikkeling, jonge ondernemer, Belgische ondernemer, Gent, technologie">
    <meta name="author" content="Léon Missoul">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://www.leonmissoul.be">
    <link rel="alternate" hreflang="nl-BE" href="https://www.leonmissoul.be">
    <link rel="alternate" hreflang="nl-BE" href="https://leonmissoul.be">
    <link rel="alternate" hreflang="nl-BE" href="https://leonmissoul.com">
    <link rel="alternate" hreflang="x-default" href="https://www.leonmissoul.be">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://www.leonmissoul.be">
    <meta property="og:title" content="Léon Missoul | Software Ontwikkelaar & Ondernemer uit Brugge">
    <meta property="og:description" content="Student Industrieel Ingenieur aan de Universiteit Gent met specialisatie in software. Oprichter van Crash Events artiestenbureau en een IT-servicebedrijf. Jonge ondernemer met passie voor technologie en innovatie.">
    <meta property="og:image" content="https://www.leonmissoul.be/lovable-uploads/me4.png">
    <meta property="og:site_name" content="Léon Missoul">
    <meta property="og:locale" content="nl_BE">
    <meta property="og:locale:alternate" content="nl_NL">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://www.leonmissoul.be">
    <meta property="twitter:title" content="Léon Missoul | Software Ontwikkelaar & Ondernemer uit Brugge">
    <meta property="twitter:description" content="Student Industrieel Ingenieur aan de Universiteit Gent met specialisatie in software. Oprichter van Crash Events artiestenbureau en een IT-servicebedrijf. Jonge ondernemer met passie voor technologie en innovatie.">
    <meta property="twitter:image" content="https://www.leonmissoul.be/lovable-uploads/me4.png">
    <meta name="twitter:creator" content="@missoulleon">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- JSON-LD Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Person",
      "name": "Léon Missoul",
      "url": "https://www.leonmissoul.be",
      "image": "https://www.leonmissoul.be/lovable-uploads/me4.png",
      "sameAs": [
        "https://leonmissoul.be",
        "https://leonmissoul.com",
        "https://www.linkedin.com/in/l%C3%A9on-missoul-293b95179/",
        "https://www.instagram.com/missoulleon/",
        "https://github.com/missoulleon"
      ],
      "jobTitle": "Software Ontwikkelaar & Ondernemer",
      "worksFor": [
        {
          "@type": "Organization",
          "name": "Crash Events",
          "description": "Artiestenbureau",
          "@id": "https://www.crashevents.be"
        },
        {
          "@type": "Organization",
          "name": "IT Servicebedrijf",
          "description": "Webontwikkeling en IT diensten"
        }
      ],
      "alumniOf": {
        "@type": "CollegeOrUniversity",
        "name": "Universiteit Gent",
        "department": "Industrieel Ingenieur",
        "address": {
          "@type": "PostalAddress",
          "addressLocality": "Gent",
          "addressRegion": "Oost-Vlaanderen",
          "addressCountry": "BE"
        }
      },
      "description": "Student Industrieel Ingenieur aan de Universiteit Gent met specialisatie in software. Oprichter van Crash Events artiestenbureau en een IT-servicebedrijf. Jonge ondernemer met passie voor technologie en innovatie.",
      "knowsLanguage": ["nl", "en", "fr"],
      "email": "<EMAIL>",
      "telephone": "+32472972106"
    }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
